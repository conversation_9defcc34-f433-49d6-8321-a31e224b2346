package com.wutos.dloongsee.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wutos.dloongsee.api.entity.PassEvent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;

/**
 * <p>
 * 通行障碍事件
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/21
 */
@Mapper
public interface PassEventMapper extends BaseMapper<PassEvent> {
    /**
     * 根据eventId更新endTime, endMil
     */
    Integer updateEndTimeAndEndMil(@Param("endTime") LocalDateTime endTime, @Param("endMil") Integer endMil, @Param("eventId") String eventId);
}