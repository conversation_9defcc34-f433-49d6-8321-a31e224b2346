package com.wutos.dloongsee.api.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wutos.dloongsee.api.entity.Road;
import com.wutos.dloongsee.api.entity.RoadRamp;
import com.wutos.dloongsee.api.entity.RoadSegment;
import com.wutos.dloongsee.api.entity.RoadStructure;
import com.wutos.dloongsee.api.mapper.RoadMapper;
import com.wutos.dloongsee.api.mapper.RoadRampMapper;
import com.wutos.dloongsee.api.mapper.RoadSegmentMapper;
import com.wutos.dloongsee.api.mapper.RoadStructureMapper;
import com.wutos.dloongsee.api.vo.RoadSegmentDefineVO;
import com.wutos.dloongsee.api.vo.RoadSegmentVO;
import com.wutos.dloongsee.api.vo.RoadStructureVO;
import com.wutos.dloongsee.common.enums.StructureType;
import lombok.Getter;
import org.mapstruct.Named;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.google.common.collect.Lists.newArrayList;

/**
 * <p>
 * 道路
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/14
 */
@Service
public class RoadService {
    @Autowired
    private RoadSegmentMapper roadSegmentMapper;
    @Autowired
    private RoadRampMapper roadRampMapper;
    @Autowired
    private RoadStructureMapper roadStructureMapper;

    @Getter
    private List<RoadSegment> roadSegments;
    @Autowired
    private RoadMapper roadMapper;


    @PostConstruct
    public void init() {
        roadSegments = roadSegmentMapper.selectList(null);
    }

    /**
     * 查询路段定义
     *
     * @param id 路段id
     * @return 路段定义
     */
    public RoadSegmentDefineVO getRoadSegmentDefine(Integer id) {
        RoadSegment roadSegment = roadSegmentMapper.selectById(id);
        List<RoadRamp> ramps = roadRampMapper.selectList(Wrappers.lambdaQuery(RoadRamp.class).eq(RoadRamp::getSegmentId, id));
        List<RoadStructure> structures = roadStructureMapper.selectList(Wrappers.lambdaQuery(RoadStructure.class).eq(RoadStructure::getSegmentId, id));
        RoadSegment pre = roadSegments.stream().filter(e -> e.getId() == id - 1).findFirst().orElse(null);
        RoadSegment next = roadSegments.stream().filter(e -> e.getId() == id + 1).findFirst().orElse(null);
        return RoadSegmentDefineVO.fromEntity(roadSegment, ramps, structures, pre, next);
    }

    /**
     * 获取全部道路分段信息(gis 地图用)
     *
     * @return 所有路段定义
     */
    public List<RoadSegmentDefineVO> getAllRoadSegmentDefine() {
        return roadSegments.stream().map(RoadSegmentDefineVO::fromEntity).collect(Collectors.toList());
    }


    /**
     * 获取全域基础设施数据
     *
     * @return 基础设施数据
     */
    public List<RoadStructureVO> getAllStructure() {
        //gis 上只显示这几座桥
        List<String> showBridges = newArrayList("府河特大桥", "后官湖特大桥", "红河大桥", "黄土岗特大桥");

        List<RoadStructure> roadStructures = roadStructureMapper.selectList(null);

        return roadStructures.stream().filter(roadStructure -> {
            if (roadStructure.getType() == StructureType.BRIDGE) {
                return showBridges.contains(roadStructure.getName());
            }
            return true;
        }).filter(distinctByKey(RoadStructure::getName)).map(RoadStructureVO::fromEntity).collect(Collectors.toList());
    }

    /**
     * 去重
     * @param keyExtractor
     * @return
     * @param <T>
     */
    public static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return object -> seen.putIfAbsent(keyExtractor.apply(object), Boolean.TRUE) == null;
    }

    @Named("getRoadSegmentNameById")
    public String getRoadSegmentNameById(Integer id) {
        if (id == -1) {
            return "全域";
        }
        return roadSegments.stream().filter(roadSegment -> roadSegment.getId().equals(id))
                .findFirst().get().getName();
    }

    public List<RoadSegmentVO> getAllRoadSegmentDict() {
        List<RoadSegmentVO> result = roadSegments.stream()
                .map(roadSegment -> new RoadSegmentVO(roadSegment.getId(), roadSegment.getName()))
                .collect(Collectors.toList());
        result.add(0, new RoadSegmentVO(-1, "全域"));
        return result;
    }

    public List<Road> getRoadInfo() {
        return roadMapper.selectList(null);
    }
}
