package com.wutos.dloongsee.api.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wutos.dloongsee.common.enums.EventCategory;
import com.wutos.dloongsee.common.enums.EventType;
import com.wutos.dloongsee.common.enums.RoadDirection;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 实时事件
 * <AUTHOR>
 */
@Data
@Builder
public class RtEventVO {
    /**
     * 事件id
     */
    private String eventId;

    /**
     * 车辆id
     */
    private String carId;

    /**
     * 车牌
     */
    private String carNum;

    /**
     * 车道号
     */
    private Integer wn;

    /**
     * 事件开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 事件终止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 事件起始位置
     */
    public Integer startMil;

    /**
     * 事件终止位置
     */
    public Integer endMil;

    /**
     * 数据更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 方向
     */
    private RoadDirection direction;

    /**
     * 事件类型
     */
    private EventType type;

    /**
     * 速度
     */
    private Double speed;

    private double[] startLatLng;

    private double[] endLatLng;

    private Integer level;

    private EventCategory category;
    /**
     * 事件类型名称
     */
    private String typeName;
}
