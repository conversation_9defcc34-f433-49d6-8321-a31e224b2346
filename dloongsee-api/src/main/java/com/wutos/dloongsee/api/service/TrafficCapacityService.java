package com.wutos.dloongsee.api.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wutos.dloongsee.api.config.TrafficCapacityConfig;
import com.wutos.dloongsee.api.entity.RoadSegment;
import com.wutos.dloongsee.api.entity.TrafficCapacity;
import com.wutos.dloongsee.api.entity.TrafficCapacitySegment;
import com.wutos.dloongsee.api.mapper.TrafficCapacityMapper;
import com.wutos.dloongsee.api.mapper.TrafficCapacitySegmentMapper;
import com.wutos.dloongsee.common.dto.ZMQCarTrackDto;
import com.wutos.dloongsee.common.dto.ZMQEventDTO;
import com.wutos.dloongsee.common.enums.CarType;
import com.wutos.dloongsee.common.enums.EventType;
import com.wutos.dloongsee.common.enums.RoadDirection;
import com.wutos.dloongsee.common.springevent.UnderclockedCarTrackEvent;
import com.wutos.dloongsee.common.springevent.ZMQEventSpringEvent;
import com.wutos.dloongsee.common.utils.RoadDirectionUtils;
import com.wutos.dloongsee.common.utils.TaskTimeUtils;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 通行能力
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/20
 */
@Service
@EnableConfigurationProperties(TrafficCapacityConfig.class)
public class TrafficCapacityService {

    @Autowired
    private TrafficCapacityConfig config;
    @Autowired
    private RoadService roadService;
    @Autowired
    private TrafficCapacitySegmentMapper trafficCapacitySegmentMapper;
    @Autowired
    private TrafficCapacityMapper trafficCapacityMapper;
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    private List<TrafficCapacitySegment> trafficCapacitySegments;
    private Map<Integer, AggregationCache> cacheUp;
    private Map<Integer, AggregationCache> cacheDown;
    private final Map<String, CarStatus> carStatusUpMap = new HashMap<>();
    private final Map<String, CarStatus> carStatusDownMap = new HashMap<>();
    private List<ZMQEventDTO> lastEventsUp = new ArrayList<>();
    private List<ZMQEventDTO> lastEventsDown = new ArrayList<>();

    @PostConstruct
    public void init() {
        trafficCapacitySegments = trafficCapacitySegmentMapper.selectList(Wrappers.lambdaQuery(TrafficCapacitySegment.class).orderByAsc(TrafficCapacitySegment::getStartMil));
        cacheUp = trafficCapacitySegments.stream().collect(Collectors.toMap(TrafficCapacitySegment::getId, AggregationCache::new));
        cacheDown = trafficCapacitySegments.stream().collect(Collectors.toMap(TrafficCapacitySegment::getId, AggregationCache::new));
    }

    @Async("trafficCapacityThreadPoolTaskExecutor")
    @EventListener
    public void process(UnderclockedCarTrackEvent underclockedCarTrackEvent) {
        List<ZMQCarTrackDto> trackUp = underclockedCarTrackEvent.getUp();
        List<ZMQCarTrackDto> trackDown = underclockedCarTrackEvent.getDown();
        aggregation(trackUp, cacheUp, carStatusUpMap, RoadDirection.UP);
        aggregation(trackDown, cacheDown, carStatusDownMap, RoadDirection.DOWN);
    }

    @Scheduled(cron = "${traffic-capacity.cron}")
    public void statistics() {
        LocalDateTime time = TaskTimeUtils.getLastExecutionTime(LocalDateTime.now(), config.getDuration());
        List<TrafficCapacity> capacitiesUp = cacheUp.values().stream().map(aggregationCache -> statistics(aggregationCache, RoadDirection.UP, time)).collect(Collectors.toList());
        List<TrafficCapacity> capacitiesDown = cacheDown.values().stream().map(aggregationCache -> statistics(aggregationCache, RoadDirection.DOWN, time)).collect(Collectors.toList());
        lastEventsUp = buildAndPublishEvent(capacitiesUp, RoadDirection.UP, lastEventsUp);
        lastEventsDown = buildAndPublishEvent(capacitiesDown, RoadDirection.DOWN, lastEventsDown);
        trafficCapacityMapper.insertBatch(Stream.concat(capacitiesUp.stream(), capacitiesDown.stream()).collect(Collectors.toList()));
    }

    private void aggregation(List<ZMQCarTrackDto> tracks, Map<Integer, AggregationCache> cache, Map<String, CarStatus> carStatusMap, RoadDirection direction) {
        boolean milAsc = (RoadDirectionUtils.isMilFromLeft() && direction == RoadDirection.DOWN) || (!RoadDirectionUtils.isMilFromLeft() && direction == RoadDirection.UP);
        Integer firstId = milAsc ? trafficCapacitySegments.get(0).getId() : trafficCapacitySegments.get(trafficCapacitySegments.size() - 1).getId();
        Integer lastId = milAsc ? trafficCapacitySegments.get(trafficCapacitySegments.size() - 1).getId() : trafficCapacitySegments.get(0).getId();
        // 处理结束的轨迹
        carStatusMap.entrySet().removeIf(entry -> {
            String id = entry.getKey();
            CarStatus carStatus = entry.getValue();
            boolean remove = tracks.stream().noneMatch(e -> Objects.equals(e.getId(), id));
            if (remove) {
                // 从最后一段离开则聚合
                Integer passId = carStatus.getCapacitySegmentId();
                if (Objects.equals(passId, lastId)) {
                    cache.get(passId).add(carStatus.getLastTime() - carStatus.getEntryTime(), carStatus.getCarType());
                }
            }
            return remove;
        });
        for (ZMQCarTrackDto track : tracks) {
            TrafficCapacitySegment currentCapacitySegment = trafficCapacitySegments.stream()
                    .filter(e -> track.getMil() > e.getStartMil() && track.getMil() < e.getEndMil()).findFirst().orElse(null);
            if (currentCapacitySegment == null) {
                continue;
            }
            // 第一帧
            if (!carStatusMap.containsKey(track.getId())) {
                carStatusMap.put(track.getId(), new CarStatus(track, currentCapacitySegment.getId(), track.getMessageTicks()));
                continue;
            }
            CarStatus carStatus = carStatusMap.get(track.getId());
            carStatus.updateFrame(track.getMessageTicks());
            // 判断路段是否变化
            Integer passId = carStatus.getCapacitySegmentId();
            if (Objects.equals(passId, currentCapacitySegment.getId())) {
                continue;
            }
            // 如果上高速后的第一段不为整条路的第一段, 不聚合
            if (!carStatus.isInEntrySegment() || Objects.equals(passId, firstId)) {
                cache.get(passId).add(track.getMessageTicks() - carStatus.getEntryTime(), carStatus.getCarType());
            }
            // 更新车辆状态
            carStatus.updateSegment(currentCapacitySegment.getId(), track.getMessageTicks());
        }
    }

    private TrafficCapacity statistics(AggregationCache aggregationCache, RoadDirection direction, LocalDateTime createTime) {
        int num = aggregationCache.getNum();
        double time = (double) aggregationCache.getTime() / 1000;
        int ve = aggregationCache.getVe();
        aggregationCache.reset();
        double speedAvg = config.getFreeSpeed() / 3.6;
        double timeAge = (aggregationCache.getCapacitySegment().getEndMil() - aggregationCache.getCapacitySegment().getStartMil()) / speedAvg;
        if (num > 0) {
            timeAge = time / num;
            speedAvg = (num * (aggregationCache.getCapacitySegment().getEndMil() - aggregationCache.getCapacitySegment().getStartMil())) / time;
        }
        RoadSegment roadSegment = roadService.getRoadSegments().stream().filter(e -> Objects.equals(e.getId(), aggregationCache.getCapacitySegment().getSegmentId())).findFirst().get();
        // 拥堵等级判断, 样本量不少于车道数(排除应急车道)的10倍
        int laneCount = roadSegment.getHasEmergencyLane() == 1 ? roadSegment.getLaneCount() - 1 : roadSegment.getLaneCount();
        int level = 4;
        if (num >= laneCount * 10) {
            for (int i = 1; i <= config.getCongestionLevel().size(); i++) {
                TrafficCapacityConfig.CongestionThreshold congestionThreshold = config.getCongestionLevel().get(i);
                if (speedAvg * 3.6 <= congestionThreshold.getSpeed()) {
                    level = i;
                    break;
                }
            }
        }
        TrafficCapacity trafficCapacity = new TrafficCapacity();
        trafficCapacity.setDirection(direction);
        trafficCapacity.setNum(num);
        trafficCapacity.setVe(ve);
        trafficCapacity.setTimeAvg(timeAge);
        trafficCapacity.setSpeedAvg(speedAvg);
        trafficCapacity.setCongestionLevel(level);
        trafficCapacity.setStartMil(aggregationCache.getCapacitySegment().getStartMil());
        trafficCapacity.setEndMil(aggregationCache.getCapacitySegment().getEndMil());
        trafficCapacity.setSegmentId(aggregationCache.getCapacitySegment().getSegmentId());
        trafficCapacity.setCreateTime(createTime);
        return trafficCapacity;
    }

    private List<ZMQEventDTO> buildAndPublishEvent(List<TrafficCapacity> capacities, RoadDirection direction, List<ZMQEventDTO> lastEvents) {
        capacities.sort(Comparator.comparing(TrafficCapacity::getStartMil));
        // 合并连续拥堵路段
        List<ZMQEventDTO> newEvents = new ArrayList<>();
        for (TrafficCapacity capacity : capacities) {
            if (capacity.getCongestionLevel() == 4) {
                continue;
            }
            if (!newEvents.isEmpty() && Objects.equals(newEvents.get(newEvents.size() - 1).getLevel(), capacity.getCongestionLevel())) {
                newEvents.get(newEvents.size() - 1).setEndMil(capacity.getEndMil());
            } else {
                ZMQEventDTO dto = new ZMQEventDTO();
                dto.setStartTime(capacity.getCreateTime());
                dto.setStartMil(capacity.getStartMil());
                dto.setEndMil(capacity.getEndMil());
                dto.setUpdateTime(capacity.getCreateTime());
                dto.setType(EventType.CONGESTION_MAIN);
                dto.setLevel(capacity.getCongestionLevel());
                newEvents.add(dto);
            }
        }
        // 结束事件：lastEvents 有但 newEvents 没有
        for (ZMQEventDTO last : lastEvents) {
            if (newEvents.stream().noneMatch(newE -> congestionSame(last, newE))) {
                last.setEndTime(capacities.get(0).getCreateTime());
                last.setIsStarted(false);
                applicationEventPublisher.publishEvent(new ZMQEventSpringEvent(last));
            }
        }
        for (ZMQEventDTO now : newEvents) {
            ZMQEventDTO matched = lastEvents.stream().filter(last -> congestionSame(last, now)).findFirst().orElse(null);
            if (matched != null) {
                // 持续, 沿用id
                now.setEventId(matched.getEventId());
            } else {
                // 开始事件：newEvents 有但 lastEvents 没有
                now.setEventId(now.getType() + IdWorker.get32UUID() + "@" + direction);
                now.setIsStarted(true);
                applicationEventPublisher.publishEvent(new ZMQEventSpringEvent(now));
            }
        }
        return newEvents;
    }

    /**
     * 判断拥堵事件是否相同
     */
    private static boolean congestionSame(ZMQEventDTO e1, ZMQEventDTO e2) {
        return Objects.equals(e1.getStartMil(), e2.getStartMil())
                && Objects.equals(e1.getEndMil(), e2.getEndMil())
                && Objects.equals(e1.getLevel(), e2.getLevel());
    }

    @Data
    private static class CarStatus {
        private CarType carType;
        private Integer capacitySegmentId;
        private long entryTime;
        private long lastTime;
        // 是否处在上高速后的第一段
        private boolean inEntrySegment;

        public CarStatus(ZMQCarTrackDto carTrackDto, Integer capacitySegmentId, long time) {
            this.carType = carTrackDto.getCarType();
            this.capacitySegmentId = capacitySegmentId;
            this.entryTime = time;
            this.lastTime = time;
            this.inEntrySegment = true;
        }

        public void updateSegment(Integer capacitySegmentId, long entryTime) {
            this.capacitySegmentId = capacitySegmentId;
            this.entryTime = entryTime;
            this.inEntrySegment = false;
        }

        public void updateFrame(long time) {
            this.lastTime = time;
        }
    }

    @Data
    private static class AggregationCache {
        private TrafficCapacitySegment capacitySegment;
        private int num;
        private long time;
        // 当量交通量
        private int ve;

        public AggregationCache(TrafficCapacitySegment capacitySegment) {
            this.capacitySegment = capacitySegment;
            this.num = 0;
            this.time = 0;
            this.ve = 0;
        }

        public void add(long timeDelta, CarType carType) {
            this.num++;
            this.time += timeDelta;
            this.ve += carType == CarType.SMALL ? 1 : 3;
        }

        public void reset() {
            this.num = 0;
            this.time = 0;
            this.ve = 0;
        }
    }
}
