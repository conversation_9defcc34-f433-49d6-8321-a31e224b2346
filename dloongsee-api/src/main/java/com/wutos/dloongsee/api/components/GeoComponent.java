package com.wutos.dloongsee.api.components;

import com.wutos.dloongsee.api.entity.Road;
import com.wutos.dloongsee.api.mapper.RoadMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Arrays;

@Component
public class GeoComponent {

    @Autowired
    private RoadMapper roadMapper;

    private double[][] ROAD_LNG_LATS;

    private int START_MIL;

    private int END_MIL;

    @PostConstruct
    public void init() {
        Road road = roadMapper.selectList(null).get(0);
        START_MIL = road.getStartMil();
        END_MIL = road.getEndMil();
        String lngLats = road.getLnglats();
        ROAD_LNG_LATS = Arrays.stream(lngLats.split("\\|"))
                .map(pair -> Arrays.stream(pair.split(","))
                        .mapToDouble(Double::parseDouble)
                        .toArray())
                .toArray(double[][]::new);
    }

    public double[] milToLngLat(int mil) {
        if (mil < START_MIL || mil > END_MIL) {
            return null;
        }
        double targetDist = END_MIL - mil; // 从 END_MIL 出发的距离
        double sumDist = 0;

        for (int i = 0; i < ROAD_LNG_LATS.length - 1; i++) {
            double[] p1 = ROAD_LNG_LATS[i];
            double[] p2 = ROAD_LNG_LATS[i + 1];
            double segDist = distance(p1, p2);

            if (sumDist + segDist >= targetDist) {
                double ratio = (targetDist - sumDist) / segDist;
                double lng = p1[0] + ratio * (p2[0] - p1[0]);
                double lat = p1[1] + ratio * (p2[1] - p1[1]);
                return new double[]{lng, lat};
            }
            sumDist += segDist;
        }
        return ROAD_LNG_LATS[ROAD_LNG_LATS.length - 1];
    }

    private double distance(double[] p1, double[] p2) {
        double R = 6371000; // 地球半径（米）
        double radLat1 = Math.toRadians(p1[1]);
        double radLat2 = Math.toRadians(p2[1]);
        double deltaLat = radLat2 - radLat1;
        double deltaLng = Math.toRadians(p2[0] - p1[0]);

        double a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
                Math.cos(radLat1) * Math.cos(radLat2) *
                        Math.sin(deltaLng / 2) * Math.sin(deltaLng / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;
    }

}
