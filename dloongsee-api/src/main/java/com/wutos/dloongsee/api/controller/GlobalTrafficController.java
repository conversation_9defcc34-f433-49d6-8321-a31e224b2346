package com.wutos.dloongsee.api.controller;

import com.wutos.dloongsee.api.service.GlobalTrafficService;
import com.wutos.dloongsee.api.vo.BaseResponse;
import com.wutos.dloongsee.api.vo.GlobalTrafficAvgTravelSpeedLineVO;
import com.wutos.dloongsee.api.vo.GlobalTrafficAvgTravelTimeBarVO;
import com.wutos.dloongsee.api.vo.GlobalTrafficFlowAnalysisVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/global/traffic")
public class GlobalTrafficController {

    @Autowired
    private GlobalTrafficService globalTrafficService;

    /**
     * 全域交通态势专题交通流量分析
     * @return GlobalTrafficFlowAnalysisVO
     */
    @GetMapping("/trafficFlowAnalysis")
    public BaseResponse<GlobalTrafficFlowAnalysisVO> getTrafficFlowAnalysis() {
        return BaseResponse.ok(globalTrafficService.getTrafficFlowAnalysis());
    }

    /**
     * 全域交通态势专题行程速度曲线
     * @return GlobalTrafficAvgSpeedLineVO
     */
    @GetMapping("/avgTravelSpeedLine")
    public BaseResponse<GlobalTrafficAvgTravelSpeedLineVO> getAvgTravelSpeedLine() {
        return BaseResponse.ok(globalTrafficService.getAvgTravelSpeedLine());
    }

    /**
     * 全域交通态势专题平均行程时间
     * @return GlobalTrafficAvgTravelTimeBarVO
     */
    @GetMapping("/avgTravelTimeBar")
    public BaseResponse<GlobalTrafficAvgTravelTimeBarVO> getAvgTimeBar() {
        return BaseResponse.ok(globalTrafficService.getAvgTravelTimeBar());
    }
}
