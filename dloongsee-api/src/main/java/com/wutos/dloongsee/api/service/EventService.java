package com.wutos.dloongsee.api.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wutos.dloongsee.api.controller.reqeust.EventRequest;
import com.wutos.dloongsee.api.entity.DriveEvent;
import com.wutos.dloongsee.api.entity.PassEvent;
import com.wutos.dloongsee.api.entity.RoadSegment;
import com.wutos.dloongsee.api.entity.TrafficEvent;
import com.wutos.dloongsee.api.mapper.DriveEventMapper;
import com.wutos.dloongsee.api.mapper.PassEventMapper;
import com.wutos.dloongsee.api.mapper.TrafficEventMapper;
import com.wutos.dloongsee.api.mapstruct.EventMapperStruct;
import com.wutos.dloongsee.api.vo.EventVO;
import com.wutos.dloongsee.api.vo.PageResponse;
import com.wutos.dloongsee.api.websocket.EventWebSocket;
import com.wutos.dloongsee.common.dto.ZMQEventDTO;
import com.wutos.dloongsee.common.enums.EventCategory;
import com.wutos.dloongsee.common.enums.EventType;
import com.wutos.dloongsee.common.springevent.EventWsOpenEvent;
import com.wutos.dloongsee.common.springevent.ZMQEventSpringEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.WebSocketSession;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 事件
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/16
 */
@Service
@Slf4j
public class EventService {
    private static final String EVENT_REDIS_PREFIX = "event:";

    @Autowired
    private DriveEventMapper driveEventMapper;
    @Autowired
    private PassEventMapper passEventMapper;
    @Autowired
    private TrafficEventMapper trafficEventMapper;
    @Autowired
    private EventWebSocket eventWebSocket;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private EventMapperStruct eventMapperStruct;
    @Autowired
    private RoadService roadService;

    @Async("eventThreadPoolTaskExecutor")
    @EventListener
    public void processEvent(ZMQEventSpringEvent zmqEventSpringEvent) {
        ZMQEventDTO zmqEventDTO = zmqEventSpringEvent.getZmqEventDTO();
        EventType eventType = zmqEventDTO.getType();
        switch (eventType.getCategory()) {
            case DRIVE:
                //驾驶异常
                if (zmqEventDTO.getIsStarted()) {
                    redisTemplate.opsForValue().set(EVENT_REDIS_PREFIX + zmqEventDTO.getEventId(), zmqEventDTO);
                    saveDriveEvent(zmqEventDTO);
                } else {
                    redisTemplate.delete(EVENT_REDIS_PREFIX + zmqEventDTO.getEventId());
                    driveEventMapper.updateEndTimeAndEndMil(zmqEventDTO.getEndTime(), zmqEventDTO.getEndMil(), zmqEventDTO.getEventId());
                }
                break;
            case PASS:
                //通行障碍
                if (zmqEventDTO.getIsStarted()) {
                    redisTemplate.opsForValue().set(EVENT_REDIS_PREFIX + zmqEventDTO.getEventId(), zmqEventDTO);
                    savePassEvent(zmqEventDTO);
                } else {
                    redisTemplate.delete(EVENT_REDIS_PREFIX + zmqEventDTO.getEventId());
                    passEventMapper.updateEndTimeAndEndMil(zmqEventDTO.getEndTime(), zmqEventDTO.getEndMil(), zmqEventDTO.getEventId());
                }
                break;
            case TRAFFIC:
                //流量异常
                if (zmqEventDTO.getIsStarted()) {
                    redisTemplate.opsForValue().set(EVENT_REDIS_PREFIX + zmqEventDTO.getEventId(), zmqEventDTO);
                    saveTrafficEvent(zmqEventDTO);
                } else {
                    redisTemplate.delete(EVENT_REDIS_PREFIX + zmqEventDTO.getEventId());
                    trafficEventMapper.updateEndTimeAndEndMil(zmqEventDTO.getEndTime(), zmqEventDTO.getEndMil(), zmqEventDTO.getEventId());
                }
                break;
            default:
                log.error("类型匹配失败，当前事件类型：{}", eventType.getCategory());
        }
        sendMessage(null);
    }

    @EventListener
    public void wsOpen(EventWsOpenEvent event) {
        sendMessage(event.getSession());
    }

    private void sendMessage(WebSocketSession session) {
        Set<String> keys = redisTemplate.keys(EVENT_REDIS_PREFIX + "*");
        List<ZMQEventDTO> eventDtoList = redisTemplate.opsForValue().multiGet(keys);
        eventWebSocket.sendMessage(eventDtoList, session);
    }

    private void saveDriveEvent(ZMQEventDTO zmqEventDTO) {
        DriveEvent driveEvent = new DriveEvent();
        driveEvent.setCarId(zmqEventDTO.getCarId());
        driveEvent.setEventId(zmqEventDTO.getEventId());
        driveEvent.setEventType(zmqEventDTO.getType());
        driveEvent.setCarNum(zmqEventDTO.getCarNum());
        driveEvent.setStartMil(zmqEventDTO.getStartMil());
        driveEvent.setEndMil(zmqEventDTO.getEndMil());
        driveEvent.setStartTime(zmqEventDTO.getStartTime());
        driveEvent.setEndTime(zmqEventDTO.getEndTime());
        driveEvent.setSegmentId(getRoadSegmentIdByMil(zmqEventDTO.getStartMil()));
        driveEvent.setDirection(zmqEventDTO.getRoadDirection());
        driveEvent.setWn(zmqEventDTO.getLane());
        driveEvent.setSpeed(zmqEventDTO.getSpeed());
        driveEventMapper.insert(driveEvent);
    }

    private void savePassEvent(ZMQEventDTO zmqEventDTO) {
        PassEvent passEvent = new PassEvent();
        passEvent.setEventId(zmqEventDTO.getEventId());
        passEvent.setEventType(zmqEventDTO.getType());
        passEvent.setStartMil(zmqEventDTO.getStartMil());
        passEvent.setEndMil(zmqEventDTO.getEndMil());
        passEvent.setStartTime(zmqEventDTO.getStartTime());
        passEvent.setEndTime(zmqEventDTO.getEndTime());
        passEvent.setSegmentId(getRoadSegmentIdByMil(zmqEventDTO.getStartMil()));
        passEvent.setDirection(zmqEventDTO.getRoadDirection());
        passEvent.setWn(zmqEventDTO.getLane());
        passEventMapper.insert(passEvent);
    }

    private void saveTrafficEvent(ZMQEventDTO zmqEventDTO) {
        TrafficEvent trafficEvent = new TrafficEvent();
        trafficEvent.setEventId(zmqEventDTO.getEventId());
        trafficEvent.setEventType(zmqEventDTO.getType());
        trafficEvent.setLevel(zmqEventDTO.getLevel());
        trafficEvent.setDirection(zmqEventDTO.getRoadDirection());
        trafficEvent.setStartMil(zmqEventDTO.getStartMil());
        trafficEvent.setEndMil(zmqEventDTO.getEndMil());
        trafficEvent.setStartTime(zmqEventDTO.getStartTime());
        trafficEvent.setEndTime(zmqEventDTO.getEndTime());
        trafficEvent.setSegmentId(getRoadSegmentIdByMil(zmqEventDTO.getStartMil()));
        trafficEventMapper.insert(trafficEvent);
    }

    /**
     * 获取里程所在路段id
     *
     * @param mil 里程
     * @return 路段id
     */
    private Integer getRoadSegmentIdByMil(Integer mil) {
        return roadService.getRoadSegments().stream()
                .filter(e -> mil >= e.getStartMil() && mil <= e.getEndMil())
                .findFirst()
                .map(RoadSegment::getId)
                .orElse(null);
    }

    /**
     * 根据条件分页查询事件数据
     *
     * @param eventRequest 查询条件
     * @return 分页查询结果
     */
    public PageResponse<EventVO> findByRequest(EventRequest eventRequest) {
        EventCategory eventCategory = eventRequest.getCategory();
        Page<?> page = new Page<>(eventRequest.getPageNum(), eventRequest.getPageSize());

        switch (eventCategory) {
            case DRIVE:
                return queryEvents(eventRequest, page, driveEventMapper,
                        DriveEvent::getEventType, DriveEvent::getSegmentId, DriveEvent::getStartTime);
            case PASS:
                return queryEvents(eventRequest, page, passEventMapper,
                        PassEvent::getEventType, PassEvent::getSegmentId, PassEvent::getStartTime);
            case TRAFFIC:
                return queryEvents(eventRequest, page, trafficEventMapper,
                        TrafficEvent::getEventType, TrafficEvent::getSegmentId, TrafficEvent::getStartTime);
            default:
                log.error("类型匹配失败，当前事件类型：{}", eventCategory);
                throw new IllegalArgumentException("不支持的事件类型：" + eventCategory);
        }
    }

    /**
     * 通用事件查询方法
     *
     * @param eventRequest    查询条件
     * @param page            分页对象
     * @param mapper          对应的Mapper
     * @param eventTypeGetter 事件类型字段getter
     * @param segmentIdGetter 路段ID字段getter
     * @param startTimeGetter 开始时间字段getter
     * @param <T>             事件实体类型
     * @return 查询结果
     */
    private <T> PageResponse<EventVO> queryEvents(EventRequest eventRequest, Page<?> page, BaseMapper<T> mapper,
                                    SFunction<T, EventType> eventTypeGetter,
                                    SFunction<T, Integer> segmentIdGetter,
                                    SFunction<T, LocalDateTime> startTimeGetter) {
        LambdaQueryWrapper<T> queryWrapper = new LambdaQueryWrapper<>();

        // 事件类型条件
        if (eventRequest.getEventTypes() != null && !eventRequest.getEventTypes().isEmpty()) {
            queryWrapper.in(eventTypeGetter, eventRequest.getEventTypes());
        }

        // 路段ID条件
        if (eventRequest.getSegmentId() != null && eventRequest.getSegmentId() != -1) {
            queryWrapper.eq(segmentIdGetter, eventRequest.getSegmentId());
        }

        // 时间范围条件
        if (eventRequest.getStartTime() != null) {
            queryWrapper.ge(startTimeGetter, eventRequest.getStartTime().atStartOfDay());
        }
        if (eventRequest.getEndTime() != null) {
            queryWrapper.le(startTimeGetter, eventRequest.getEndTime().atTime(23, 59, 59));
        }

        // 按开始时间倒序排列
        queryWrapper.orderByDesc(startTimeGetter);
        Page<T> result = mapper.selectPage((Page<T>) page, queryWrapper);

        List<EventVO> collect = result.getRecords().stream().map(event -> {
            if (event instanceof DriveEvent) {
                return eventMapperStruct.toVO((DriveEvent) event);
            } else if (event instanceof PassEvent) {
                return eventMapperStruct.toVO((PassEvent) event);
            } else if (event instanceof TrafficEvent) {
                return eventMapperStruct.toVO((TrafficEvent) event);
            } else {
                return null;
            }
        }).collect(Collectors.toList());
        return new PageResponse<>(result.getTotal(), result.getCurrent(), result.getSize(), collect);
    }
}


