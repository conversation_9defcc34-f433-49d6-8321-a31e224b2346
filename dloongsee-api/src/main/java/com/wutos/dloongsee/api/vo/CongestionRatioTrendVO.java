package com.wutos.dloongsee.api.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 拥堵里程占比趋势统计VO
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CongestionRatioTrendVO {

    /**
     * 周数据列表，包含4周的数据
     */
    private List<WeeklyData> weeks;

    /**
     * 周数据
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class WeeklyData {
        
        /**
         * 周标识（第一周、第二周、第三周、第四周）
         */
        private String weekLabel;
        
        /**
         * 一周7天的拥堵占比数据
         */
        private List<DailyRatio> dailyRatios;
    }

    /**
     * 每日拥堵占比数据
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DailyRatio {
        
        /**
         * 星期几（周一、周二、周三、周四、周五、周六、周日）
         */
        private String dayOfWeek;
        
        /**
         * 拥堵占比百分数（0-100%）
         */
        private BigDecimal ratio;
    }
}
