<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wutos.dloongsee.api.mapper.TrafficEventMapper">
    <resultMap id="BaseResultMap" type="com.wutos.dloongsee.api.entity.TrafficEvent">
        <!--@mbg.generated-->
        <!--@Table traffic_event-->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="event_id" jdbcType="VARCHAR" property="eventId" />
        <result column="event_type" jdbcType="VARCHAR" property="eventType" />
        <result column="level" jdbcType="INTEGER" property="level" />
        <result column="direction" jdbcType="VARCHAR" property="direction" />
        <result column="start_mil" jdbcType="INTEGER" property="startMil" />
        <result column="end_mil" jdbcType="INTEGER" property="endMil" />
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
        <result column="dispose_time" jdbcType="TIMESTAMP" property="disposeTime" />
        <result column="dispose_status" jdbcType="INTEGER" property="disposeStatus" />
        <result column="segment_id" jdbcType="INTEGER" property="segmentId" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, event_id, event_type, `level`, direction, start_mil, end_mil, start_time, end_time,
        dispose_time, dispose_status, segment_id
    </sql>

    <update id="updateEndTimeAndEndMil">
        update traffic_event
        set end_time = #{endTime},
            end_mil  = #{endMil}
        where event_id = #{eventId}
    </update>
</mapper>